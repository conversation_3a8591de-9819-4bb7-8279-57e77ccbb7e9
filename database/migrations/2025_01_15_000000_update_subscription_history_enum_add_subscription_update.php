<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Update the enum to include missing values and the new 'subscription_update' value
        DB::statement("ALTER TABLE `subscription_history` MODIFY `history_event` ENUM('new', 'renewal', 'suspension', 'termination', 'assigned_quota_change', 'remaining_quota_change', 'payment_update', 'payment_approved', 'payment_rejected', 'license_key', 'activation', 'auto_renew_setting_change', 'extension', 'subscription_update')");
    }

    public function down(): void
    {
        // Revert back to the original enum values (as they were in the original migration)
        DB::statement("ALTER TABLE `subscription_history` MODIFY `history_event` ENUM('renewal', 'suspension', 'termination', 'assigned_quota_change', 'remaining_quota_change', 'payment_update', 'license_key', 'activation', 'auto_renew_setting_change', 'extension')");
    }
};
